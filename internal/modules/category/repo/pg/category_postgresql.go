package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type CategoryPostgreRepo interface {
	Create(ctx context.Context, category model.Category) error
	Update(ctx context.Context, category model.Category) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Category, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Category, error)
	Delete(ctx context.Context, id string) error
	GetSubcategories(ctx context.Context, categoryID string) ([]model.Category, error)
	GetCategoryChildrenByParentCode(ctx context.Context, parentCode string) ([]model.Category, error)
	GetParentCategories(ctx context.Context) ([]model.Category, error)
	GetParentCategory(ctx context.Context, categoryID string) (*model.Category, error)
}

type categoryPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewCategoryPostgreRepo(pool *pgxpool.Pool) CategoryPostgreRepo {
	return &categoryPostgreRepo{
		pool: pool,
	}
}
