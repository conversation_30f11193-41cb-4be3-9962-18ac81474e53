package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
)

type categoryUsecase struct {
	repo model.CategoryRepository
}

// Delete implements model.CategoryUsecase.
func (c *categoryUsecase) Delete(ctx context.Context, id string) error {
	return c.repo.Delete(ctx, id)
}

// GetAll implements model.CategoryUsecase.
func (c *categoryUsecase) GetAll(ctx context.Context) ([]model.Category, error) {
	return c.repo.GetAll(ctx)
}

// GetByProp implements model.CategoryUsecase.
func (c *categoryUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Category, error) {
	return c.repo.GetByProp(ctx, prop, value)
}

// GetSubcategories implements model.CategoryUsecase.
func (c *categoryUsecase) GetSubcategories(ctx context.Context, categoryID string) ([]model.Category, error) {
	return c.repo.GetSubcategories(ctx, categoryID)
}

// GetSubcategoriesByCode implements model.CategoryUsecase.
func (c *categoryUsecase) GetSubcategoriesByCode(ctx context.Context, categoryCode string) ([]model.Category, error) {
	// First, find the category by code
	category, err := c.repo.GetByProp(ctx, "code", categoryCode)
	if err != nil {
		return nil, err
	}

	// Then get its subcategories using the category ID
	return c.repo.GetSubcategories(ctx, category.ID)
}

// GetCategoryChildrenByParentCode implements model.CategoryUsecase.
func (c *categoryUsecase) GetCategoryChildrenByParentCode(ctx context.Context, parentCode string) ([]model.Category, error) {
	return c.repo.GetCategoryChildrenByParentCode(ctx, parentCode)
}

// GetParentCategories implements model.CategoryUsecase.
func (c *categoryUsecase) GetParentCategories(ctx context.Context) ([]model.Category, error) {
	return c.repo.GetParentCategories(ctx)
}

// GetCategoryWithDetailsByCode implements model.CategoryUsecase.
func (c *categoryUsecase) GetCategoryWithDetailsByCode(ctx context.Context, categoryCode string) (*model.CategoryWithSubcategories, error) {
	// First, find the category by code
	category, err := c.repo.GetByProp(ctx, "code", categoryCode)
	if err != nil {
		return nil, err
	}

	// Get subcategories using the category ID
	subcategories, err := c.repo.GetSubcategories(ctx, category.ID)
	if err != nil {
		return nil, err
	}

	// Get parent category using the category ID
	parent, err := c.repo.GetParentCategory(ctx, category.ID)
	if err != nil {
		return nil, err
	}

	return &model.CategoryWithSubcategories{
		Category:      *category,
		Subcategories: subcategories,
		Parent:        parent,
	}, nil
}

// ValidateCode implements model.CategoryUsecase.
func (c *categoryUsecase) ValidateCode(ctx context.Context, code string) error {
	count, err := c.repo.CountByProp(ctx, "code", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.CategoryConflictCodef("Category code already exists", nil, nil)
	}

	return nil
}

func NewCategoryUsecase(repo model.CategoryRepository) model.CategoryUsecase {
	return &categoryUsecase{
		repo: repo,
	}
}
