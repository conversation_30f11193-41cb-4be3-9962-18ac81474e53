package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements CategoryHandler.
func (c *categoryHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	category, err := c.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get category by id")
		return
	}

	rest.SuccessDResponse(w, r, categoryToResult(category), http.StatusOK)
}

// GetAll implements CategoryHandler.
func (c *categoryHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	categories, err := c.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get all categories")
		return
	}

	results := make([]categoryResult, len(categories))
	for i, category := range categories {
		results[i] = categoryToResult(&category)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetSubcategories implements CategoryHandler.
func (c *categoryHandler) GetSubcategories(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	subcategories, err := c.useCase.GetSubcategories(ctx, id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get subcategories")
		return
	}

	results := make([]categoryResult, len(subcategories))
	for i, category := range subcategories {
		results[i] = categoryToResult(&category)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetSubcategoriesByCode implements CategoryHandler.
func (c *categoryHandler) GetSubcategoriesByCode(w http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	subcategories, err := c.useCase.GetSubcategoriesByCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get subcategories by code")
		return
	}

	results := make([]categoryResult, len(subcategories))
	for i, category := range subcategories {
		results[i] = categoryToResult(&category)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetCategoryChildrenByParentCode implements CategoryHandler.
func (c *categoryHandler) GetCategoryChildrenByParentCode(w http.ResponseWriter, r *http.Request) {
	parentCode := r.PathValue("parentCode")
	ctx := r.Context()

	children, err := c.useCase.GetCategoryChildrenByParentCode(ctx, parentCode)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get category children by parent code")
		return
	}

	results := make([]categoryResult, len(children))
	for i, category := range children {
		results[i] = categoryToResult(&category)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetParentCategories implements CategoryHandler.
func (c *categoryHandler) GetParentCategories(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	parentCategories, err := c.useCase.GetParentCategories(ctx)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get parent categories")
		return
	}

	results := make([]categoryResult, len(parentCategories))
	for i, category := range parentCategories {
		results[i] = categoryToResult(&category)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetCategoryWithDetails implements CategoryHandler.
func (c *categoryHandler) GetCategoryWithDetails(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	details, err := c.useCase.GetCategoryWithDetails(ctx, id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get category with details")
		return
	}

	rest.SuccessDResponse(w, r, categoryWithDetailsToResult(details), http.StatusOK)
}

// GetCategoryWithDetailsByCode implements CategoryHandler.
func (c *categoryHandler) GetCategoryWithDetailsByCode(w http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	details, err := c.useCase.GetCategoryWithDetailsByCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get category with details by code")
		return
	}

	rest.SuccessDResponse(w, r, categoryWithDetailsToResult(details), http.StatusOK)
}
