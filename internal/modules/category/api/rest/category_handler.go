package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type CategoryHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	GetSubcategories(w http.ResponseWriter, r *http.Request)
	GetSubcategoriesByCode(w http.ResponseWriter, r *http.Request)
	GetCategoryChildrenByParentCode(w http.ResponseWriter, r *http.Request)
	GetParentCategories(w http.ResponseWriter, r *http.Request)
	GetCategoryWithDetails(w http.ResponseWriter, r *http.Request)
	GetCategoryWithDetailsByCode(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
}

type categoryHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.CategoryUsecase
}

func NewCategoryHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.CategoryUsecase,
) CategoryHandler {
	return &categoryHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
