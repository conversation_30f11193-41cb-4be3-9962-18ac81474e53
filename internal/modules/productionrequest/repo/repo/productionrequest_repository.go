package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/repo/pg"
)

type productionRequestRepository struct {
	pgRepo pg.ProductionRequestPostgreRepo
}

func NewProductionRequestRepository(pgRepo pg.ProductionRequestPostgreRepo) model.ProductionRequestRepository {
	return &productionRequestRepository{
		pgRepo: pgRepo,
	}
}

// CountByProp implements model.ProductionRequestRepository.
func (p *productionRequestRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return p.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.ProductionRequestRepository.
func (p *productionRequestRepository) Create(ctx context.Context, productionRequest model.ProductionRequest) error {
	return p.pgRepo.Create(ctx, productionRequest)
}

// Delete implements model.ProductionRequestRepository.
func (p *productionRequestRepository) Delete(ctx context.Context, id string) error {
	return p.pgRepo.Delete(ctx, id)
}

// GetAll implements model.ProductionRequestRepository.
func (p *productionRequestRepository) GetAll(ctx context.Context) ([]model.ProductionRequest, error) {
	return p.pgRepo.GetAll(ctx)
}

// GetByProp implements model.ProductionRequestRepository.
func (p *productionRequestRepository) GetByProp(ctx context.Context, prop string, value string) (*model.ProductionRequest, error) {
	return p.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.ProductionRequestRepository.
func (p *productionRequestRepository) Update(ctx context.Context, productionRequest model.ProductionRequest) error {
	return p.pgRepo.Update(ctx, productionRequest)
}

// GetProductionRequestItems implements model.ProductionRequestRepository.
func (p *productionRequestRepository) GetProductionRequestItems(ctx context.Context, productionRequestID string) ([]model.ProductionRequestItem, error) {
	return p.pgRepo.GetProductionRequestItems(ctx, productionRequestID)
}

// CreateProductionRequestItems implements model.ProductionRequestRepository.
func (p *productionRequestRepository) CreateProductionRequestItems(ctx context.Context, productionRequestID string, items []model.ProductionRequestItemCreate) error {
	return p.pgRepo.CreateProductionRequestItems(ctx, productionRequestID, items)
}

// UpdateProductionRequestItems implements model.ProductionRequestRepository.
func (p *productionRequestRepository) UpdateProductionRequestItems(ctx context.Context, productionRequestID string, items []model.ProductionRequestItemUpdate) error {
	return p.pgRepo.UpdateProductionRequestItems(ctx, productionRequestID, items)
}

// DeleteProductionRequestItems implements model.ProductionRequestRepository.
func (p *productionRequestRepository) DeleteProductionRequestItems(ctx context.Context, productionRequestID string) error {
	return p.pgRepo.DeleteProductionRequestItems(ctx, productionRequestID)
}
