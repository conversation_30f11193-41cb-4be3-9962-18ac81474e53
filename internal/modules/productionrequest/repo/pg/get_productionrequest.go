package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *productionRequestPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.ProductionRequest, error) {
	var productionRequest model.ProductionRequest
	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, code, client_id, expected_date, priority, state, created_at, updated_at, deleted_at
			FROM production_requests
			WHERE ` + prop + ` = $1 AND deleted_at IS NULL
		`

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&productionRequest.ID,
			&productionRequest.Code,
			&productionRequest.ClientID,
			&productionRequest.ExpectedDate,
			&productionRequest.Priority,
			&productionRequest.State,
			&productionRequest.CreatedAt,
			&productionRequest.UpdatedAt,
			&productionRequest.DeletedAt,
		)

		if err != nil {
			return utils.InternalErrorf("failed to get production request", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Get production request items
	items, err := p.GetProductionRequestItems(ctx, productionRequest.ID)
	if err != nil {
		return nil, err
	}
	productionRequest.Requests = items

	return &productionRequest, nil
}

func (p *productionRequestPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `SELECT COUNT(*) FROM production_requests WHERE ` + prop + ` = $1 AND deleted_at IS NULL`
		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)
		if err != nil {
			return utils.InternalErrorf("failed to count production requests", err, nil)
		}
		return nil
	})

	return count, err
}

func (p *productionRequestPostgreRepo) GetAll(ctx context.Context) ([]model.ProductionRequest, error) {
	var productionRequests []model.ProductionRequest
	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, code, client_id, expected_date, priority, state, created_at, updated_at, deleted_at
			FROM production_requests
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get production requests", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var pr model.ProductionRequest
			err := rows.Scan(
				&pr.ID,
				&pr.Code,
				&pr.ClientID,
				&pr.ExpectedDate,
				&pr.Priority,
				&pr.State,
				&pr.CreatedAt,
				&pr.UpdatedAt,
				&pr.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan production request", err, nil)
			}

			// Get production request items for each request
			items, err := p.GetProductionRequestItems(ctx, pr.ID)
			if err != nil {
				return err
			}
			pr.Requests = items

			productionRequests = append(productionRequests, pr)
		}

		return nil
	})

	return productionRequests, err
}
