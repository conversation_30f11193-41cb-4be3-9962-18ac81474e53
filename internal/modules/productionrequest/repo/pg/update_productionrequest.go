package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/oklog/ulid/v2"
)

func (p *productionRequestPostgreRepo) Update(ctx context.Context, productionRequest model.ProductionRequest) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Start transaction
		tx, err := conn.Begin(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to begin transaction", err, nil)
		}
		defer tx.Rollback(ctx)

		// Update production request
		query := `
			UPDATE production_requests SET
				code = $2, client_id = $3, expected_date = $4, priority = $5, state = $6, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1
		`
		_, err = tx.Exec(ctx, query,
			productionRequest.ID,
			productionRequest.Code,
			productionRequest.ClientID,
			productionRequest.ExpectedDate,
			productionRequest.Priority,
			productionRequest.State,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update production request", err, nil)
		}

		// Delete existing production request items
		deleteItemsQuery := `DELETE FROM production_request_items WHERE production_request_id = $1`
		_, err = tx.Exec(ctx, deleteItemsQuery, productionRequest.ID)
		if err != nil {
			return utils.InternalErrorf("failed to delete existing production request items", err, nil)
		}

		// Insert new production request items
		if len(productionRequest.Requests) > 0 {
			for _, item := range productionRequest.Requests {
				itemID := ulid.Make().String()
				itemQuery := `
					INSERT INTO production_request_items (
						id, production_request_id, product_id, quantity
					)
					VALUES ($1, $2, $3, $4)
				`
				_, err = tx.Exec(ctx, itemQuery, itemID, productionRequest.ID, item.ProductID, item.Quantity)
				if err != nil {
					return utils.InternalErrorf("failed to create production request item", err, nil)
				}
			}
		}

		// Commit transaction
		err = tx.Commit(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to commit transaction", err, nil)
		}

		return nil
	})
}
