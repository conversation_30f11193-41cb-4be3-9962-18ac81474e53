package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/oklog/ulid/v2"
)

func (p *productionRequestPostgreRepo) GetProductionRequestItems(ctx context.Context, productionRequestID string) ([]model.ProductionRequestItem, error) {
	var items []model.ProductionRequestItem
	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, production_request_id, product_id, quantity
			FROM production_request_items
			WHERE production_request_id = $1
			ORDER BY created_at ASC
		`

		rows, err := conn.Query(ctx, query, productionRequestID)
		if err != nil {
			return utils.InternalErrorf("failed to get production request items", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var item model.ProductionRequestItem
			err := rows.Scan(
				&item.ID,
				&item.ProductionRequestID,
				&item.ProductID,
				&item.Quantity,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan production request item", err, nil)
			}
			items = append(items, item)
		}

		return nil
	})

	return items, err
}

func (p *productionRequestPostgreRepo) CreateProductionRequestItems(ctx context.Context, productionRequestID string, items []model.ProductionRequestItemCreate) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		for _, item := range items {
			itemID := ulid.Make().String()
			query := `
				INSERT INTO production_request_items (
					id, production_request_id, product_id, quantity
				)
				VALUES ($1, $2, $3, $4)
			`
			_, err := conn.Exec(ctx, query, itemID, productionRequestID, item.ProductID, item.Quantity)
			if err != nil {
				return utils.InternalErrorf("failed to create production request item", err, nil)
			}
		}
		return nil
	})
}

func (p *productionRequestPostgreRepo) UpdateProductionRequestItems(ctx context.Context, productionRequestID string, items []model.ProductionRequestItemUpdate) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Start transaction
		tx, err := conn.Begin(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to begin transaction", err, nil)
		}
		defer tx.Rollback(ctx)

		// Delete existing items
		deleteQuery := `DELETE FROM production_request_items WHERE production_request_id = $1`
		_, err = tx.Exec(ctx, deleteQuery, productionRequestID)
		if err != nil {
			return utils.InternalErrorf("failed to delete existing production request items", err, nil)
		}

		// Insert updated items
		for _, item := range items {
			itemID := item.ID
			if itemID == "" {
				itemID = ulid.Make().String()
			}
			insertQuery := `
				INSERT INTO production_request_items (
					id, production_request_id, product_id, quantity
				)
				VALUES ($1, $2, $3, $4)
			`
			_, err = tx.Exec(ctx, insertQuery, itemID, productionRequestID, item.ProductID, item.Quantity)
			if err != nil {
				return utils.InternalErrorf("failed to create production request item", err, nil)
			}
		}

		// Commit transaction
		err = tx.Commit(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to commit transaction", err, nil)
		}

		return nil
	})
}

func (p *productionRequestPostgreRepo) DeleteProductionRequestItems(ctx context.Context, productionRequestID string) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `DELETE FROM production_request_items WHERE production_request_id = $1`
		_, err := conn.Exec(ctx, query, productionRequestID)
		if err != nil {
			return utils.InternalErrorf("failed to delete production request items", err, nil)
		}
		return nil
	})
}
