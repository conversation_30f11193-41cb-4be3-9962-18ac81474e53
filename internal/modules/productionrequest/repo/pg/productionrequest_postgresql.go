package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ProductionRequestPostgreRepo interface {
	Create(ctx context.Context, productionRequest model.ProductionRequest) error
	Update(ctx context.Context, productionRequest model.ProductionRequest) error
	GetByProp(ctx context.Context, prop string, value string) (*model.ProductionRequest, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.ProductionRequest, error)
	Delete(ctx context.Context, id string) error
	GetProductionRequestItems(ctx context.Context, productionRequestID string) ([]model.ProductionRequestItem, error)
	CreateProductionRequestItems(ctx context.Context, productionRequestID string, items []model.ProductionRequestItemCreate) error
	UpdateProductionRequestItems(ctx context.Context, productionRequestID string, items []model.ProductionRequestItemUpdate) error
	DeleteProductionRequestItems(ctx context.Context, productionRequestID string) error
}

type productionRequestPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewProductionRequestPostgreRepo(pool *pgxpool.Pool) ProductionRequestPostgreRepo {
	return &productionRequestPostgreRepo{
		pool: pool,
	}
}
