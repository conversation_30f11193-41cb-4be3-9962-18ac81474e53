package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
)

// ValidateCode implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) ValidateCode(ctx context.Context, code string) error {
	count, err := p.repo.CountByProp(ctx, "code", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.ProductionRequestConflictCodef("Production request code already exists", nil, nil)
	}

	return nil
}
