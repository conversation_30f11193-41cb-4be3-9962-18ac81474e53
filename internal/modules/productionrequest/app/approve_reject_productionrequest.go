package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
)

// Approve implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) Approve(ctx context.Context, id string) error {
	// Check if production request exists
	productionRequest, err := p.repo.GetByProp(ctx, "id", id)
	if err != nil {
		return model.ProductionRequestNotFoundf("Production request not found", err, nil)
	}

	// Validate that the request is in pending state
	if productionRequest.State != "pending" {
		return model.ProductionRequestInvalidStateTransitionf("Production request can only be approved when in pending state. Current state: "+productionRequest.State, nil, nil)
	}

	// Update the state to approved
	productionRequest.State = "approved"
	return p.repo.Update(ctx, *productionRequest)
}

// Reject implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) Reject(ctx context.Context, id string) error {
	// Check if production request exists
	productionRequest, err := p.repo.GetByProp(ctx, "id", id)
	if err != nil {
		return model.ProductionRequestNotFoundf("Production request not found", err, nil)
	}

	// Validate that the request is in pending state
	if productionRequest.State != "pending" {
		return model.ProductionRequestInvalidStateTransitionf("Production request can only be rejected when in pending state. Current state: "+productionRequest.State, nil, nil)
	}

	// Update the state to rejected
	productionRequest.State = "rejected"
	return p.repo.Update(ctx, *productionRequest)
}
