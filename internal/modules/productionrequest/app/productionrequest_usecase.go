package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	clientModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
)

type productionRequestUsecase struct {
	repo        model.ProductionRequestRepository
	clientRepo  clientModel.ClientRepository
	productRepo productModel.ProductRepository
}

func NewProductionRequestUsecase(
	repo model.ProductionRequestRepository,
	clientRepo clientModel.ClientRepository,
	productRepo productModel.ProductRepository,
) model.ProductionRequestUsecase {
	return &productionRequestUsecase{
		repo:        repo,
		clientRepo:  clientRepo,
		productRepo: productRepo,
	}
}

// Delete implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) Delete(ctx context.Context, id string) error {
	return p.repo.Delete(ctx, id)
}

// GetAll implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) GetAll(ctx context.Context) ([]model.ProductionRequest, error) {
	return p.repo.GetAll(ctx)
}

// GetByProp implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.ProductionRequest, error) {
	return p.repo.GetByProp(ctx, prop, value)
}
