package model

import "context"

type ProductionRequestRepository interface {
	Create(ctx context.Context, productionRequest ProductionRequest) error
	Update(ctx context.Context, productionRequest ProductionRequest) error
	GetByProp(ctx context.Context, prop string, value string) (*ProductionRequest, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]ProductionRequest, error)
	Delete(ctx context.Context, id string) error
	GetProductionRequestItems(ctx context.Context, productionRequestID string) ([]ProductionRequestItem, error)
	CreateProductionRequestItems(ctx context.Context, productionRequestID string, items []ProductionRequestItemCreate) error
	UpdateProductionRequestItems(ctx context.Context, productionRequestID string, items []ProductionRequestItemUpdate) error
	DeleteProductionRequestItems(ctx context.Context, productionRequestID string) error
}
