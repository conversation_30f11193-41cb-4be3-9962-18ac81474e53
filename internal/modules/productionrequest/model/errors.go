package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	ProductionRequestConflictCode utils.ErrCode = utils.ProductionRequestCode + iota
	ProductionRequestConflictCodeCode
	ProductionRequestNotFoundCode
	ProductionRequestInvalidClientCode
	ProductionRequestInvalidProductCode
	ProductionRequestInvalidPriorityCode
	ProductionRequestInvalidStateCode
	ProductionRequestInvalidDateCode
	ProductionRequestInvalidQuantityCode
	ProductionRequestInvalidStateTransitionCode
)

func ProductionRequestConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestConflictCode, message, err, details)
}

func ProductionRequestConflictCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestConflictCodeCode, message, err, details)
}

func ProductionRequestNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestNotFoundCode, message, err, details)
}

func ProductionRequestInvalidClientf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestInvalidClientCode, message, err, details)
}

func ProductionRequestInvalidProductf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestInvalidProductCode, message, err, details)
}

func ProductionRequestInvalidPriorityf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestInvalidPriorityCode, message, err, details)
}

func ProductionRequestInvalidStatef(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestInvalidStateCode, message, err, details)
}

func ProductionRequestInvalidDatef(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestInvalidDateCode, message, err, details)
}

func ProductionRequestInvalidQuantityf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestInvalidQuantityCode, message, err, details)
}

func ProductionRequestInvalidStateTransitionf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductionRequestInvalidStateTransitionCode, message, err, details)
}
