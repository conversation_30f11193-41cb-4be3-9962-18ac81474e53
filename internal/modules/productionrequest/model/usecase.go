package model

import "context"

type ProductionRequestUsecase interface {
	Create(ctx context.Context, productionRequest ProductionRequestCreate) (string, error)
	Update(ctx context.Context, productionRequest ProductionRequestUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*ProductionRequest, error)
	GetAll(ctx context.Context) ([]ProductionRequest, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
	Approve(ctx context.Context, id string) error
	Reject(ctx context.Context, id string) error
}
