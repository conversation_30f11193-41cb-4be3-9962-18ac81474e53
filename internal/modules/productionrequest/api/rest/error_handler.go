package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.ProductionRequestConflictCode:               http.StatusConflict,
	model.ProductionRequestConflictCodeCode:           http.StatusConflict,
	model.ProductionRequestNotFoundCode:               http.StatusNotFound,
	model.ProductionRequestInvalidClientCode:          http.StatusBadRequest,
	model.ProductionRequestInvalidProductCode:         http.StatusBadRequest,
	model.ProductionRequestInvalidPriorityCode:        http.StatusBadRequest,
	model.ProductionRequestInvalidStateCode:           http.StatusBadRequest,
	model.ProductionRequestInvalidDateCode:            http.StatusBadRequest,
	model.ProductionRequestInvalidQuantityCode:        http.StatusBadRequest,
	model.ProductionRequestInvalidStateTransitionCode: http.StatusBadRequest,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
