package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type productionRequestUpdate struct {
	ID           string                           `json:"id" validate:"required"`
	Code         string                           `json:"code" validate:"required"`
	ClientID     string                           `json:"client_id" validate:"required"`
	ExpectedDate *time.Time                       `json:"expected_date"`
	Priority     string                           `json:"priority" validate:"required"`
	State        string                           `json:"state" validate:"required"`
	Requests     []productionRequestItemUpdateDTO `json:"requests" validate:"required,min=1,dive"`
}

type productionRequestItemUpdateDTO struct {
	ID        string  `json:"id"`
	ProductID string  `json:"product_id" validate:"required"`
	Quantity  float64 `json:"quantity" validate:"required,gt=0"`
}

func productionRequestUpdateToModel(dto productionRequestUpdate) model.ProductionRequestUpdate {
	requests := make([]model.ProductionRequestItemUpdate, len(dto.Requests))
	for i, item := range dto.Requests {
		requests[i] = model.ProductionRequestItemUpdate{
			ID:        item.ID,
			ProductID: item.ProductID,
			Quantity:  item.Quantity,
		}
	}

	return model.ProductionRequestUpdate{
		ID:           dto.ID,
		Code:         dto.Code,
		ClientID:     dto.ClientID,
		ExpectedDate: dto.ExpectedDate,
		Priority:     dto.Priority,
		State:        dto.State,
		Requests:     requests,
	}
}

// Update implements ProductionRequestHandler.
func (p *productionRequestHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[productionRequestUpdate](w, r, p.validator)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		return
	}

	err = p.useCase.Update(ctx, productionRequestUpdateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to update production request")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
