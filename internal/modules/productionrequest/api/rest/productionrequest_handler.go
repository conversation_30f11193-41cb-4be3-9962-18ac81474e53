package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type ProductionRequestHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
	Approve(w http.ResponseWriter, r *http.Request)
	Reject(w http.ResponseWriter, r *http.Request)
}

type productionRequestHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.ProductionRequestUsecase
}

func NewProductionRequestHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.ProductionRequestUsecase,
) ProductionRequestHandler {
	return &productionRequestHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
