package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type productionRequestResult struct {
	ID           string                            `json:"id"`
	Code         string                            `json:"code"`
	ClientID     string                            `json:"client_id"`
	ExpectedDate *time.Time                        `json:"expected_date"`
	Priority     string                            `json:"priority"`
	State        string                            `json:"state"`
	Requests     []productionRequestItemResultDTO  `json:"requests"`
	CreatedAt    *time.Time                        `json:"created_at"`
	UpdatedAt    *time.Time                        `json:"updated_at"`
}

type productionRequestItemResultDTO struct {
	ID                  string  `json:"id"`
	ProductionRequestID string  `json:"production_request_id"`
	ProductID           string  `json:"product_id"`
	Quantity            float64 `json:"quantity"`
}

func productionRequestToResult(pr model.ProductionRequest) productionRequestResult {
	requests := make([]productionRequestItemResultDTO, len(pr.Requests))
	for i, item := range pr.Requests {
		requests[i] = productionRequestItemResultDTO{
			ID:                  item.ID,
			ProductionRequestID: item.ProductionRequestID,
			ProductID:           item.ProductID,
			Quantity:            item.Quantity,
		}
	}

	return productionRequestResult{
		ID:           pr.ID,
		Code:         pr.Code,
		ClientID:     pr.ClientID,
		ExpectedDate: pr.ExpectedDate,
		Priority:     pr.Priority,
		State:        pr.State,
		Requests:     requests,
		CreatedAt:    pr.CreatedAt,
		UpdatedAt:    pr.UpdatedAt,
	}
}

// GetById implements ProductionRequestHandler.
func (p *productionRequestHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	productionRequest, err := p.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get production request")
		return
	}

	rest.SuccessDResponse(w, r, productionRequestToResult(*productionRequest), http.StatusOK)
}

// GetAll implements ProductionRequestHandler.
func (p *productionRequestHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	productionRequests, err := p.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get production requests")
		return
	}

	var results []productionRequestResult
	for _, pr := range productionRequests {
		results = append(results, productionRequestToResult(pr))
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
