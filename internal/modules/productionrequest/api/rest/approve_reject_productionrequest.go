package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Approve implements ProductionRequestHandler.
func (p *productionRequestHandler) Approve(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	err := p.useCase.Approve(ctx, id)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to approve production request")
		return
	}

	rest.SuccessDResponse(w, r, "Production request approved successfully", http.StatusOK)
}

// Reject implements ProductionRequestHandler.
func (p *productionRequestHandler) Reject(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	err := p.useCase.Reject(ctx, id)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to reject production request")
		return
	}

	rest.SuccessDResponse(w, r, "Production request rejected successfully", http.StatusOK)
}
