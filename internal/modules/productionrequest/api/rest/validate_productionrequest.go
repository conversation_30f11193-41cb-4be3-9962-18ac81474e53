package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateCode implements ProductionRequestHandler.
func (p *productionRequestHandler) ValidateCode(w http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	err := p.useCase.ValidateCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Code validation failed")
		return
	}

	rest.SuccessDResponse(w, r, "Code is available", http.StatusOK)
}
