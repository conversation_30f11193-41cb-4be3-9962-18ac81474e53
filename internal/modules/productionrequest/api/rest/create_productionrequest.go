package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type productionRequestCreate struct {
	Code         string                           `json:"code" validate:"required"`
	ClientID     string                           `json:"client_id" validate:"required"`
	ExpectedDate *time.Time                       `json:"expected_date"`
	Priority     string                           `json:"priority"`
	State        string                           `json:"state"`
	Requests     []productionRequestItemCreateDTO `json:"requests" validate:"required,min=1,dive"`
}

type productionRequestItemCreateDTO struct {
	ProductID string  `json:"product_id" validate:"required"`
	Quantity  float64 `json:"quantity" validate:"required,gt=0"`
}

func productionRequestCreateToModel(dto productionRequestCreate) model.ProductionRequestCreate {
	priority := dto.Priority
	if priority == "" {
		priority = "medium" // Default value
	}

	state := dto.State
	if state == "" {
		state = "pending" // Default value
	}

	requests := make([]model.ProductionRequestItemCreate, len(dto.Requests))
	for i, item := range dto.Requests {
		requests[i] = model.ProductionRequestItemCreate{
			ProductID: item.ProductID,
			Quantity:  item.Quantity,
		}
	}

	return model.ProductionRequestCreate{
		Code:         dto.Code,
		ClientID:     dto.ClientID,
		ExpectedDate: dto.ExpectedDate,
		Priority:     priority,
		State:        state,
		Requests:     requests,
	}
}

// Create implements ProductionRequestHandler.
func (p *productionRequestHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[productionRequestCreate](w, r, p.validator)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		return
	}

	id, err := p.useCase.Create(ctx, productionRequestCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to create production request")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
